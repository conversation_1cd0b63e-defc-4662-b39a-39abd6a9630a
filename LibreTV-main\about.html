<!DOCTYPE html>
<html lang="zh">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>关于我们 - LibreTV</title>
	<script src="libs/tailwindcss.min.js"></script>
	<link rel="stylesheet" href="css/styles.css">
	<link rel="manifest" href="manifest.json">
	
	<!-- Favicon -->
    <link rel="icon" href="image/logo.png">
    <link rel="apple-touch-icon" href="image/logo-black.png">
</head>
<body class="page-bg text-white flex flex-col min-h-screen">
	<header class="border-b border-[#333] bg-[#0a0a0a] p-4">
        <div class="container mx-auto flex items-center">
            <div class="flex items-center">
                <a href="/" class="flex items-center">
                    <svg class="w-8 h-8 mr-2 text-[#00ccff]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                    </svg>
                    <h1 class="text-xl font-bold gradient-text">LibreTV</h1>
                </a>
            </div>
            <div class="flex-1 text-center">
                <h2 class="text-xl font-semibold">关于LibreTV</h2>
            </div>
            <div class="flex items-center">
                <a href="/" class="px-4 py-2 bg-[#222] hover:bg-[#333] border border-[#333] rounded-lg transition-colors flex items-center">
                    <svg class="w-5 h-5 mr-1" viewBox="0 0 24 24" fill="#ffffff" xmlns="http://www.w3.org/2000/svg">
                        <path d="M10 19l-7-7m0 0l7-7m-7 7h18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    回到首页
                </a>
            </div>
        </div>
    </header>	<main class="flex-grow container mx-auto px-4 sm:px-6 py-8 sm:py-16">
		<div class="max-w-5xl mx-auto">
			<!-- 主要内容区域 -->
			<div class="bg-gradient-to-br from-[#111] to-[#0a0a0a] border border-[#333] rounded-xl sm:rounded-2xl p-4 sm:p-8 lg:p-12 shadow-2xl">
				<!-- 项目介绍 -->				
				 <div class="text-center mb-8 sm:mb-16">
					<div class="inline-flex items-center justify-center w-12 h-12 sm:w-16 sm:h-16 rounded-xl sm:rounded-2xl mb-6 sm:mb-8">
						<img src="image/logo-black.png" alt="LibreTV Logo" class="w-12 h-12 sm:w-16 sm:h-16 rounded-xl sm:rounded-2xl">
					</div>
					
					<p class="text-gray-300 text-lg sm:text-xl mb-6 sm:mb-8 leading-relaxed max-w-3xl mx-auto px-2">
						LibreTV 是一个免费的在线视频搜索平台，提供视频搜索和播放服务，致力于为用户带来最佳体验。
					</p>
					<div class="bg-[#1a1a1a] border border-[#333] rounded-lg sm:rounded-xl p-4 sm:p-6 max-w-2xl mx-auto">
						<p class="text-gray-300 text-base sm:text-lg mb-4 px-1">
							本项目代码托管在 GitHub 上，欢迎访问我们的仓库：
						</p>
						<a href="https://github.com/LibreSpark/LibreTV" class="inline-flex items-center px-4 sm:px-6 py-2 sm:py-3 bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white text-sm sm:text-base font-medium rounded-lg transition-all duration-300 transform hover:scale-105" target="_blank" rel="noopener">
							<svg class="w-4 h-4 sm:w-5 sm:h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
								<path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
							</svg>
							<span class="break-words">访问 GitHub 仓库</span>
						</a>
					</div>
				</div>				<!-- 分割线 -->
				<div class="w-full h-px bg-gradient-to-r from-transparent via-[#333] to-transparent mb-8 sm:mb-16"></div>

				<!-- 隐私政策 -->
				<div class="mb-8 sm:mb-16">
					<h2 class="text-2xl sm:text-3xl font-bold mb-6 sm:mb-8 text-center bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent">隐私政策</h2>
					<div class="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-8">
						<div class="bg-[#1a1a1a] border border-[#333] rounded-xl p-4 sm:p-8">
							<div class="flex items-center mb-3 sm:mb-4">
								<div class="w-3 h-3 bg-green-400 rounded-full mr-3"></div>
								<h3 class="text-lg sm:text-xl font-semibold text-gray-200">数据保护</h3>
							</div>
							<p class="text-gray-300 text-base sm:text-lg leading-relaxed">
								我们尊重并保护您的隐私。LibreTV 不收集任何个人数据，且不会限制访问或使用本网站。
							</p>
						</div>
						<div class="bg-[#1a1a1a] border border-[#333] rounded-xl p-4 sm:p-8">
							<div class="flex items-center mb-3 sm:mb-4">
								<div class="w-3 h-3 bg-blue-400 rounded-full mr-3"></div>
								<h3 class="text-lg sm:text-xl font-semibold text-gray-200">服务说明</h3>
							</div>
							<p class="text-gray-300 text-base sm:text-lg leading-relaxed">
								本平台仅用于提供在线视频搜索与播放服务。所有数据均由第三方接口提供，我们不会存储或追踪用户信息。
							</p>
						</div>
					</div>
				</div>

				<!-- 分割线 -->
				<div class="w-full h-px bg-gradient-to-r from-transparent via-[#333] to-transparent mb-8 sm:mb-16"></div>				<!-- 版权声明与投诉机制 -->
				<div>
					<h2 class="text-2xl sm:text-3xl font-bold mb-6 sm:mb-8 text-center bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent">版权声明与投诉机制</h2>
					<div class="space-y-6 sm:space-y-8">
						<div class="bg-[#1a1a1a] border border-[#333] rounded-xl p-4 sm:p-8">
							<div class="flex items-start">
								<div class="flex-shrink-0 w-8 h-8 bg-yellow-500 rounded-lg flex items-center justify-center mr-3 sm:mr-4 mt-1">
									<svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
									</svg>
								</div>
								<div>
									<h3 class="text-lg sm:text-xl font-semibold text-gray-200 mb-3 sm:mb-4">免责声明</h3>
									<p class="text-gray-300 text-base sm:text-lg leading-relaxed">
										LibreTV 仅提供视频搜索服务，不直接提供、存储或上传任何视频内容。所有搜索结果均来自第三方公开接口。用户在使用本站服务时，须遵守相关法律法规，不得利用搜索结果从事侵权行为，如下载、传播未经授权的作品等。
									</p>
								</div>
							</div>
						</div>
						
						<div class="bg-[#1a1a1a] border border-[#333] rounded-xl p-4 sm:p-8">
							<div class="flex items-start">
								<div class="flex-shrink-0 w-8 h-8 bg-red-500 rounded-lg flex items-center justify-center mr-3 sm:mr-4 mt-1">
									<svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 7.89a2 2 0 002.83 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
									</svg>
								</div>
								<div class="flex-1">
									<h3 class="text-lg sm:text-xl font-semibold text-gray-200 mb-3 sm:mb-4">投诉反馈</h3>
									<p class="text-gray-300 text-base sm:text-lg mb-4 sm:mb-6 leading-relaxed">
										若您是版权方或相关权利人，发现本站搜索结果中存在侵犯您合法权益的内容，请通过以下渠道向我们反馈：
									</p>
									<div class="bg-gradient-to-r from-[#222] to-[#333] border border-[#444] rounded-lg p-4 sm:p-6 mb-4 sm:mb-6">
										<div class="flex items-center flex-wrap">
											<svg class="w-5 h-5 sm:w-6 sm:h-6 text-blue-400 mr-2 sm:mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 7.89a2 2 0 002.83 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
											</svg>
											<span class="text-gray-300 text-base sm:text-lg font-medium mr-2 sm:mr-3">投诉邮箱：</span>
											<a href="mailto:<EMAIL>" class="text-blue-400 hover:text-blue-300 transition-colors text-base sm:text-lg font-medium break-all"><EMAIL></a>
										</div>
									</div>
									<p class="text-gray-300 text-base sm:text-lg leading-relaxed">
										请在投诉邮件中提供：您的身份证明、权利证明、侵权内容的具体链接及相关说明。我们将在收到投诉后尽快处理，对于确认侵权的内容，将立即断开相关链接，停止展示侵权内容，并将处理结果反馈给您。
									</p>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</main>
	<footer class="footer py-6 border-t border-[#333] bg-[#0a0a0a]">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="mb-4 md:mb-0">
                    <div class="flex items-center justify-center md:justify-start">
                        <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                        </svg>
                        <span class="gradient-text font-bold">LibreTV</span>
                    </div>
                    <p class="text-gray-500 text-sm mt-2 text-center md:text-left">© 2025 LibreTV - 自由观影，畅享精彩</p>
                </div>
                
                <div class="text-center md:text-right">
                    <p class="text-gray-500 text-sm max-w-md">
                        免责声明：本站仅为视频搜索工具，不存储、上传或分发任何视频内容。
                        所有视频均来自第三方API接口。如有侵权，请联系相关内容提供方。
                    </p>
                    <div class="mt-2 flex justify-center md:justify-end space-x-4">
                        <a href="/" class="text-gray-400 hover:text-white text-sm transition-colors">首页</a>
                        <a href="mailto:<EMAIL>" class="text-gray-400 hover:text-white text-sm transition-colors">联系方式</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>
</body>
</html>
