services:
  libretv:
    image: bestzwei/libretv:latest
    container_name: libretv
    ports:
      - "8899:8080" # 将内部 8080 端口映射到主机的 8899 端口
    environment:
      - PASSWORD=${PASSWORD:-your_password} # 可将 your_password 修改为你想要的密码，默认为 your_password
      - CORS_ORIGIN=*
      - DEBUG=false
      - REQUEST_TIMEOUT=5000
      - MAX_RETRIES=2
      - CACHE_MAX_AGE=1d
#    volumes:
#      - libretv_data:/app # 不要修改
    restart: unless-stopped

#volumes:
#  libretv_data:
#    driver: local
#    driver_opts:
#      type: none
#      o: bind
#      device: ${PWD:-.}/data # 可将 ${PWD:-.} 修改为你想要的路径，默认为当前目录下的 data 文件夹
