name: NoMore Spam

on:
  issues:
    types: [opened]
  pull_request:
    types: [opened]

permissions:
  contents: read
  issues: write
  pull-requests: write
  models: read
  actions: write

jobs:
  spam-detection:
    runs-on: ubuntu-latest
    name: NoMore Spam
    
    steps:
      - name: Fuck All the Shit
        uses: Johnson<PERSON>an/nomore-spam@main
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          
      - name: Delete workflow runs
        uses: Mattraks/delete-workflow-runs@main
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          repository: ${{ github.repository }}
          retain_days: 0
          keep_minimum_runs: 2
