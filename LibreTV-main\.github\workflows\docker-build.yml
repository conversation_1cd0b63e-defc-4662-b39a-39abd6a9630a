name: Build LibreTV image

on:
  workflow_run:
    workflows: ["Bump version"]
    types:
      - completed
  workflow_dispatch:

jobs:
  build:
    name: Build LibreTV image
    runs-on: ubuntu-latest
    if: (github.event.workflow_run.conclusion == 'success' && github.repository == 'LibreSpark/LibreTV') || (github.repository == 'bestZwei/libretv')

    steps:
      - name: Check out repository
        uses: actions/checkout@v4
      
      - name: Read version from VERSION.txt
        id: version
        run: |
          VERSION=$(cat VERSION.txt)
          echo "VERSION=$VERSION" >> $GITHUB_OUTPUT
          
      - name: Set Docker image tag based on repository
        id: set-tag
        run: |
          if [ "${{ github.repository }}" = "LibreSpark/LibreTV" ]; then
            echo "IMAGE_NAME=libretv" >> $GITHUB_OUTPUT
            echo "TAGS=${{ secrets.DOCKER_USERNAME }}/libretv:latest,${{ secrets.DOCKER_USERNAME }}/libretv:${{ steps.version.outputs.VERSION }}" >> $GITHUB_OUTPUT
          else
            echo "IMAGE_NAME=libretv-beta" >> $GITHUB_OUTPUT
            echo "TAGS=${{ secrets.DOCKER_USERNAME }}/libretv-beta:latest" >> $GITHUB_OUTPUT
          fi

      - name: Set up Docker QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Build and push LibreTV image
        uses: docker/build-push-action@v6.14.0
        with:
          context: .
          file: Dockerfile
          push: true
          tags: ${{ steps.set-tag.outputs.TAGS }}
          platforms: linux/amd64,linux/arm64/v8,linux/arm/v7